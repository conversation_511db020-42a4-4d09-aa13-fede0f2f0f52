"""
Product serializers for v2 API
"""
from rest_framework import serializers
from apps.products.models.product import Product
from apps.products.models.category import Category
from apps.products.serializers.category import CategoryListSerializer
from apps.users.models import UserTypes, Vendor


class ProductV2Serializer(serializers.ModelSerializer):
    """
    Serializer for Product model v2 API with category support
    """
    rating = serializers.ReadOnlyField()
    vendor = serializers.PrimaryKeyRelatedField(queryset=Vendor.objects.all())
    category = CategoryListSerializer(read_only=True)
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.filter(is_active=True),
        source='category',
        write_only=True,
        required=False,
        allow_null=True
    )

    class Meta:
        model = Product
        fields = [
            'id', 'vendor', 'category', 'category_id', 'title', 'capacity', 'category_type',
            'image', 'description', 'price', 'rating', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'rating', 'created_at', 'updated_at']

    def create(self, validated_data):
        """
        Create a new product instance for v2 API
        """
        request = self.context.get("request")
        user = request.user
        
        try:
            if user.user_type == UserTypes.VENDOR:
                validated_data["vendor"] = user.vendor
            elif user.user_type == UserTypes.ADMIN:
                # vendor should be provided in validated_data by admin
                if "vendor" not in validated_data or validated_data["vendor"] is None:
                    raise serializers.ValidationError({"vendor": "Vendor must be provided."})
            else:
                raise serializers.ValidationError(
                    {"message": "You do not have permission to add product"}
                )
            return super().create(validated_data)
        except Vendor.DoesNotExist as exc:
            raise serializers.ValidationError(
                {"message": "You do not have permission to add product"}
            ) from exc
        except Exception as exc:
            raise serializers.ValidationError(
                {"message": "You do not have permission to add product"}
            ) from exc


class ProductV2ListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for product list in v2 API
    """
    category = CategoryListSerializer(read_only=True)
    vendor_name = serializers.CharField(source='vendor.name', read_only=True)
    vendor_district = serializers.CharField(source='vendor.district.name', read_only=True)
    vendor_region = serializers.CharField(source='vendor.region.name', read_only=True)
    vendor_district_id = serializers.IntegerField(source='vendor.district.id', read_only=True)
    vendor_region_id = serializers.IntegerField(source='vendor.region.id', read_only=True)

    class Meta:
        model = Product
        fields = [
            'id', 'title', 'capacity', 'image', 'price', 'rating',
            'category', 'vendor_name', 'vendor_district', 'vendor_region', 
            'vendor_district_id', 'vendor_region_id', 'category_type'
        ]
