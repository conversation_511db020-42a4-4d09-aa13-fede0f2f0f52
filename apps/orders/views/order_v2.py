"""
Order views for v2 API with order items support
"""
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import permissions, status, viewsets
from rest_framework.response import Response
from rest_framework import serializers
from django.core.exceptions import PermissionDenied

from apps.orders.models.order import Order
from apps.orders.serializers.order_v2 import OrderV2CreateSerializer, OrderV2Serializer
from apps.orders.filters import OrderFilter
from apps.users.models import Customer, UserTypes


class OrderV2ViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Order model v2 API with order items support
    """
    queryset = Order.objects.all().prefetch_related('order_items__product', 'order_items__product__category')
    serializer_class = OrderV2Serializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = OrderFilter

    def get_queryset(self):
        """Filter orders based on user type and permissions"""
        user = self.request.user
        status_filter = self.request.query_params.get('status')
        base_queryset = self.queryset

        # Filter by status group if specified
        if status_filter in ['active', 'archived']:
            from apps.orders.models.order import get_status_group
            statuses = get_status_group(status_filter)
            base_queryset = base_queryset.filter(status__in=statuses)

        if not user.is_authenticated:
            return base_queryset.none()

        # Exclude orders that are in pending applications (similar to v1)
        from apps.orders.models.application import OrderApplication
        pending_application_orders = OrderApplication.objects.filter(status='pending')
        base_queryset = base_queryset.exclude(id__in=pending_application_orders)

        # Filter based on user type
        if user.user_type == UserTypes.VENDOR:
            return base_queryset.filter(vendor=user.vendor)

        if user.user_type == UserTypes.COURIER:
            try:
                courier = user.courier
                if courier.vendors.exists():
                    return base_queryset.filter(vendor__in=courier.vendors.all())
                return base_queryset.none()
            except Exception:
                return base_queryset.none()

        if user.user_type == UserTypes.CUSTOMER:
            try:
                customer = user.customer
            except Customer.DoesNotExist:
                return base_queryset.none()
            return base_queryset.filter(customer=customer)

        if user.user_type == UserTypes.ADMIN:
            return base_queryset

        raise PermissionDenied("You don't have permission to access")

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.request.method in ["POST", "PATCH"]:
            return OrderV2CreateSerializer
        return OrderV2Serializer

    def create(self, request, *args, **kwargs):
        """
        Create a new order with order items (v2 API)
        """
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            order = serializer.save()
            
            # Return the created order with full details
            response_serializer = OrderV2Serializer(order, context={'request': request})
            response_data = {
                "status": "success",
                "message": "Order created successfully",
                "data": response_serializer.data
            }
            return Response(response_data, status=status.HTTP_201_CREATED)
            
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid order data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while creating the order",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, *args, **kwargs):
        """
        Update order status and other fields (v2 API)
        Note: Order items cannot be updated after creation
        """
        try:
            instance = self.get_object()
            
            # For v2 API, we only allow status updates and some fields
            # Order items cannot be modified after creation
            allowed_fields = ['status', 'returned_bottles', 'deliverable_time']
            filtered_data = {k: v for k, v in request.data.items() if k in allowed_fields}
            
            if not filtered_data:
                return Response(
                    {
                        "status": "error",
                        "message": "No valid fields provided for update"
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Use the regular order serializer for updates (not the create serializer)
            from apps.orders.serializers.order import OrderStatusChangeSerializer
            serializer = OrderStatusChangeSerializer(instance, data=filtered_data, partial=True)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)

            # Return updated order with full details
            response_serializer = OrderV2Serializer(instance, context={'request': request})
            response_data = {
                "status": "success",
                "message": "Order updated successfully",
                "data": response_serializer.data
            }
            return Response(response_data)
            
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid order data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while updating the order",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def list(self, request, *args, **kwargs):
        """
        List orders with improved response format
        """
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An error occurred while retrieving orders",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
