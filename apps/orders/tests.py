"""
Tests for order creation with bottle logic
"""
import uuid
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

from apps.users.models import Customer, Vendor, UserTypes
from apps.products.models.product import Product
from apps.products.models.category import Category
from apps.orders.models.order import Order, OrderStatus
from apps.orders.models.order_item import OrderItem
from apps.users.vendors.models import VendorStats
from apps.helpers.models import Region, District

User = get_user_model()


class OrderV2BottleLogicTestCase(TestCase):
    """Test case for order v2 bottle logic"""

    def setUp(self):
        """Set up test data"""
        # Create regions and districts
        self.region = Region.objects.create(name="Test Region")
        self.district = District.objects.create(name="Test District", region=self.region)

        # Create vendor user and vendor
        self.vendor_user = User.objects.create_user(
            username="vendor_test",
            phone_number="+998901234567",
            user_type=UserTypes.VENDOR
        )
        self.vendor = Vendor.objects.create(
            user=self.vendor_user,
            name="Test Vendor",
            total_bottles=100,
            region=self.region,
            district=self.district
        )

        # Create vendor stats
        self.vendor_stats = VendorStats.objects.create(
            vendor=self.vendor,
            free_bottles=100,
            busy_bottles=0
        )

        # Create customer user and customer
        self.customer_user = User.objects.create_user(
            username="customer_test",
            phone_number="+998901234568",
            user_type=UserTypes.CUSTOMER
        )
        self.customer = Customer.objects.create(
            user=self.customer_user,
            fullname="Test Customer",
            region=self.region,
            district=self.district
        )

        # Create category
        self.category = Category.objects.create(
            name="Water Category",
            is_active=True
        )

        # Create water products
        self.water_product_1 = Product.objects.create(
            vendor=self.vendor,
            category=self.category,
            title="Water 19L",
            capacity=19.0,
            description="Test water product",
            price=15000,
            category_type=Product.CategoryType.WATER
        )

        self.water_product_2 = Product.objects.create(
            vendor=self.vendor,
            category=self.category,
            title="Water 5L",
            capacity=5.0,
            description="Test water product 2",
            price=8000,
            category_type=Product.CategoryType.WATER
        )

        # Create non-water product (if there were other category types)
        # For now, we only have WATER category type

        self.client = APIClient()

    def test_customer_order_creation_with_water_bottles(self):
        """Test customer order creation with water products updates bottle stats"""
        # Login as customer
        self.client.force_authenticate(user=self.customer_user)

        order_data = {
            "fullname": "Test Customer",
            "address": "Test Address",
            "phone_number": "+998901234568",
            "region": self.region.id,
            "district": self.district.id,
            "order_items": [
                {
                    "product_id": str(self.water_product_1.id),
                    "quantity": 3
                },
                {
                    "product_id": str(self.water_product_2.id),
                    "quantity": 2
                }
            ]
        }

        response = self.client.post("/api/v2/orders/orders/", order_data, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that order was created
        order = Order.objects.get(id=response.data["data"]["id"])
        self.assertEqual(order.status, OrderStatus.PENDING)
        self.assertEqual(order.customer, self.customer)
        
        # Check order items were created
        order_items = OrderItem.objects.filter(order=order)
        self.assertEqual(order_items.count(), 2)
        
        # Check bottle statistics were updated
        self.vendor_stats.refresh_from_db()
        expected_water_bottles = 3 + 2  # 3 from product_1 + 2 from product_2 = 5 bottles
        self.assertEqual(self.vendor_stats.free_bottles, 100 - expected_water_bottles)  # 95
        self.assertEqual(self.vendor_stats.busy_bottles, expected_water_bottles)  # 5

    def test_customer_order_with_old_client_return_bottles(self):
        """Test customer order creation with old client return bottles"""
        # Login as customer
        self.client.force_authenticate(user=self.customer_user)

        order_data = {
            "fullname": "Test Customer",
            "address": "Test Address",
            "phone_number": "+998901234568",
            "region": self.region.id,
            "district": self.district.id,
            "old_client_return_bottles": 5,
            "order_items": [
                {
                    "product_id": str(self.water_product_1.id),
                    "quantity": 2
                }
            ]
        }

        response = self.client.post("/api/v2/orders/orders/", order_data, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check bottle statistics were updated correctly
        self.vendor_stats.refresh_from_db()
        # 2 new bottles delivered + 5 old client bottles = 7 total busy bottles
        # free_bottles should decrease by 2 (new bottles delivered)
        self.assertEqual(self.vendor_stats.free_bottles, 100 - 2)  # 98
        self.assertEqual(self.vendor_stats.busy_bottles, 2 + 5)  # 7

    def test_vendor_order_creation_with_water_bottles(self):
        """Test vendor order creation with water products updates bottle stats"""
        # Login as vendor
        self.client.force_authenticate(user=self.vendor_user)

        order_data = {
            "customer": str(self.customer.id),
            "fullname": "Test Customer",
            "address": "Test Address",
            "phone_number": "+998901234568",
            "region": self.region.id,
            "district": self.district.id,
            "order_items": [
                {
                    "product_id": str(self.water_product_1.id),
                    "quantity": 4
                }
            ]
        }

        response = self.client.post("/api/v2/orders/orders/", order_data, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that order was created with ACCEPTED status
        order = Order.objects.get(id=response.data["data"]["id"])
        self.assertEqual(order.status, OrderStatus.ACCEPTED)
        
        # Check bottle statistics were updated
        self.vendor_stats.refresh_from_db()
        expected_water_bottles = 4
        self.assertEqual(self.vendor_stats.free_bottles, 100 - expected_water_bottles)  # 96
        self.assertEqual(self.vendor_stats.busy_bottles, expected_water_bottles)  # 4

    def test_order_without_water_products_no_bottle_update(self):
        """Test that orders without water products don't update bottle stats"""
        # Since we only have WATER category type, we'll test with 0 quantity
        # This test would be more relevant if we had other product types
        
        # Login as customer
        self.client.force_authenticate(user=self.customer_user)

        # Create order with 0 water bottles (empty order items)
        order_data = {
            "fullname": "Test Customer",
            "address": "Test Address",
            "phone_number": "+998901234568",
            "region": self.region.id,
            "district": self.district.id,
            "order_items": []
        }

        # This should fail validation, but let's test the bottle logic doesn't run
        initial_free_bottles = self.vendor_stats.free_bottles
        initial_busy_bottles = self.vendor_stats.busy_bottles

        # The request will likely fail due to empty order_items, but bottle stats shouldn't change
        response = self.client.post("/api/v2/orders/orders/", order_data, format="json")
        
        # Check bottle statistics weren't updated
        self.vendor_stats.refresh_from_db()
        self.assertEqual(self.vendor_stats.free_bottles, initial_free_bottles)
        self.assertEqual(self.vendor_stats.busy_bottles, initial_busy_bottles)

    def test_bottle_calculation_method(self):
        """Test the _calculate_water_bottles method directly"""
        from apps.orders.serializers.order_v2 import OrderV2CreateSerializer
        
        serializer = OrderV2CreateSerializer()
        
        # Test with water products
        order_items_data = [
            {"product_id": self.water_product_1.id, "quantity": 3},
            {"product_id": self.water_product_2.id, "quantity": 2}
        ]
        
        total_bottles = serializer._calculate_water_bottles(order_items_data)
        self.assertEqual(total_bottles, 5)  # 3 + 2
        
        # Test with non-existent product (should be ignored)
        order_items_data_with_invalid = [
            {"product_id": self.water_product_1.id, "quantity": 2},
            {"product_id": uuid.uuid4(), "quantity": 10}  # Non-existent product
        ]
        
        total_bottles = serializer._calculate_water_bottles(order_items_data_with_invalid)
        self.assertEqual(total_bottles, 2)  # Only valid product counted
